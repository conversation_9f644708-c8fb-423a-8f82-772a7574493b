// API endpoint to manually refresh the knowledge base
import { NextApiRequest, NextApiResponse } from 'next';
import { KnowledgeScraper } from '../../lib/knowledge-scraper';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { force = false } = req.body;
    
    const scraper = KnowledgeScraper.getInstance();
    
    const urlsToScrape = [
      'https://upzera-web.netlify.app',
      'https://upzera-web.netlify.app/about',
      'https://upzera-web.netlify.app/website-development',
      'https://upzera-web.netlify.app/chatbot-integration',
      'https://upzera-web.netlify.app/contact'
    ];

    if (force) {
      // Force refresh - ignore cache
      await scraper.forceRefresh(urlsToScrape);
    } else {
      // Normal refresh - will use cache if valid
      await scraper.buildKnowledgeBase(urlsToScrape);
    }

    const stats = scraper.getStats();

    res.status(200).json({
      success: true,
      message: force ? 'Knowledge base force refreshed' : 'Knowledge base refreshed',
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Knowledge refresh error:', error);
    res.status(500).json({
      error: 'Failed to refresh knowledge base',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
