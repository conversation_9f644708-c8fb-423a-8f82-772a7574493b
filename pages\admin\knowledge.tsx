// Admin page for managing the knowledge base
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface KnowledgeStats {
  totalChunks: number;
  lastUpdated: string;
  sources: string[];
  chunksByType: {
    webpage: number;
    static: number;
    faq: number;
  };
}

export default function KnowledgeAdmin() {
  const [stats, setStats] = useState<KnowledgeStats | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [message, setMessage] = useState('');

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/knowledge-stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const refreshKnowledge = async (force: boolean = false) => {
    setIsRefreshing(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/refresh-knowledge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ force })
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage(`✅ ${data.message}`);
        setStats(data.stats);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Failed to refresh: ${error}`);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-pink-800 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-8">Knowledge Base Admin</h1>
          
          {/* Stats Section */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Total Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.totalChunks}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Webpage Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.webpage}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Static Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.static}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">FAQ Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.faq}</p>
              </div>
            </div>
          )}

          {/* Last Updated */}
          {stats && (
            <div className="mb-8">
              <h3 className="text-white/80 text-sm font-medium mb-2">Last Updated</h3>
              <p className="text-white">{new Date(stats.lastUpdated).toLocaleString()}</p>
            </div>
          )}

          {/* Sources */}
          {stats && stats.sources.length > 0 && (
            <div className="mb-8">
              <h3 className="text-white/80 text-sm font-medium mb-2">Sources</h3>
              <ul className="text-white space-y-1">
                {stats.sources.map((source, index) => (
                  <li key={index} className="text-sm">• {source}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Actions */}
          <div className="space-y-4">
            <div className="flex gap-4">
              <Button
                onClick={() => refreshKnowledge(false)}
                disabled={isRefreshing}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isRefreshing ? 'Refreshing...' : 'Refresh Knowledge Base'}
              </Button>
              
              <Button
                onClick={() => refreshKnowledge(true)}
                disabled={isRefreshing}
                variant="destructive"
              >
                {isRefreshing ? 'Force Refreshing...' : 'Force Refresh (Ignore Cache)'}
              </Button>
              
              <Button
                onClick={fetchStats}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                Reload Stats
              </Button>
            </div>

            {message && (
              <div className="bg-white/20 rounded-lg p-4">
                <p className="text-white">{message}</p>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Instructions</h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>• <strong>Refresh Knowledge Base:</strong> Updates if cache is expired (24h)</li>
              <li>• <strong>Force Refresh:</strong> Ignores cache and re-scrapes everything</li>
              <li>• <strong>Cache Location:</strong> public/knowledge-cache.json</li>
              <li>• <strong>Auto-refresh:</strong> Happens automatically when cache expires</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
