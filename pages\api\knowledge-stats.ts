// API endpoint to get knowledge base statistics
import { NextApiRequest, NextApiResponse } from 'next';
import { KnowledgeScraper } from '../../lib/knowledge-scraper';
import { KnowledgeCacheManager } from '../../lib/knowledge-cache-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const scraper = KnowledgeScraper.getInstance();
    const cacheManager = new KnowledgeCacheManager();
    const stats = scraper.getStats();
    const knowledgeBase = scraper.getKnowledgeBase();
    const includeChunks = req.query.includeChunks === 'true';

    // Add chunks to stats if requested
    let enhancedStats = { ...stats };
    if (includeChunks) {
      const chunks = knowledgeBase.chunks.map((item, index) => ({
        id: item.id || `chunk-${index}`,
        content: item.content,
        source: item.source,
        url: '', // KnowledgeChunk doesn't have url, but we can add it
        timestamp: item.lastUpdated || new Date().toISOString(),
        wordCount: item.content.split(' ').length
      }));
      enhancedStats.chunks = chunks;
    }

    res.status(200).json({
      success: true,
      stats: enhancedStats,
      cacheInfo: {
        cacheExpiry: knowledgeBase.cacheExpiry,
        isExpired: scraper.isCacheExpired(),
        version: knowledgeBase.version
      },
      fileCache: cacheManager.getCacheStats(),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Knowledge stats error:', error);
    res.status(500).json({
      error: 'Failed to get knowledge stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
