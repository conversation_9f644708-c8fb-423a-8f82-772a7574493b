// API endpoint to get knowledge base statistics
import { NextApiRequest, NextApiResponse } from 'next';
import { KnowledgeScraper } from '../../lib/knowledge-scraper';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const scraper = KnowledgeScraper.getInstance();
    const stats = scraper.getStats();
    const knowledgeBase = scraper.getKnowledgeBase();

    res.status(200).json({
      success: true,
      stats,
      cacheInfo: {
        cacheExpiry: knowledgeBase.cacheExpiry,
        isExpired: scraper.isCacheExpired(),
        version: knowledgeBase.version
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Knowledge stats error:', error);
    res.status(500).json({
      error: 'Failed to get knowledge stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
