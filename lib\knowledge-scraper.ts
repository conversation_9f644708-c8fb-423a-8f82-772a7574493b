// Enhanced Knowledge Base System with Web Scraping
import * as cheerio from 'cheerio';

export interface KnowledgeChunk {
  id: string;
  content: string;
  source: string;
  type: 'webpage' | 'static' | 'faq';
  keywords: string[];
  lastUpdated: string;
  relevanceScore?: number;
}

export interface EnhancedKnowledge {
  chunks: KnowledgeChunk[];
  lastUpdated: string;
  sources: string[];
}

export class KnowledgeScraper {
  private static instance: KnowledgeScraper;
  private knowledgeBase: EnhancedKnowledge = {
    chunks: [],
    lastUpdated: '',
    sources: []
  };

  static getInstance(): KnowledgeScraper {
    if (!KnowledgeScraper.instance) {
      KnowledgeScraper.instance = new KnowledgeScraper();
    }
    return KnowledgeScraper.instance;
  }

  // Scrape content from multiple webpages using API endpoint
  async scrapeWebpages(urls: string[]): Promise<KnowledgeChunk[]> {
    try {
      const response = await fetch('/api/scrape-knowledge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ urls })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Scraping failed');
      }

      const allChunks: KnowledgeChunk[] = [];

      // Process each scraped page
      for (const pageData of result.data) {
        if (pageData.error) {
          console.warn(`Skipping ${pageData.url} due to error: ${pageData.error}`);
          continue;
        }

        // Create chunks from title and headings
        if (pageData.title) {
          allChunks.push({
            id: `${pageData.url}-title`,
            content: pageData.title,
            source: pageData.url,
            type: 'webpage',
            keywords: this.extractKeywords(pageData.title),
            lastUpdated: new Date().toISOString()
          });
        }

        // Create chunks from headings
        pageData.headings.forEach((heading: string, index: number) => {
          if (heading.trim().length > 10) {
            allChunks.push({
              id: `${pageData.url}-heading-${index}`,
              content: heading,
              source: pageData.url,
              type: 'webpage',
              keywords: this.extractKeywords(heading),
              lastUpdated: new Date().toISOString()
            });
          }
        });

        // Create chunks from main content
        if (pageData.content) {
          const cleanContent = this.cleanText(pageData.content);
          const textChunks = this.chunkText(cleanContent, 500);

          textChunks.forEach((chunk, index) => {
            if (chunk.trim().length > 50) {
              allChunks.push({
                id: `${pageData.url}-content-${index}`,
                content: chunk.trim(),
                source: pageData.url,
                type: 'webpage',
                keywords: this.extractKeywords(chunk),
                lastUpdated: new Date().toISOString()
              });
            }
          });
        }
      }

      return allChunks;
    } catch (error) {
      console.error('Error scraping webpages:', error);
      return [];
    }
  }

  // Clean text content
  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .replace(/[^\w\s.,!?-]/g, '') // Remove special characters except basic punctuation
      .trim();
  }

  // Split text into chunks
  private chunkText(text: string, maxLength: number): string[] {
    const sentences = text.split(/[.!?]+/);
    const chunks: string[] = [];
    let currentChunk = '';
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (!trimmedSentence) continue;
      
      if (currentChunk.length + trimmedSentence.length > maxLength) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
          currentChunk = trimmedSentence;
        }
      } else {
        currentChunk += (currentChunk ? '. ' : '') + trimmedSentence;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }

  // Extract keywords from text
  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3); // Only words longer than 3 chars
    
    // Remove common stop words
    const stopWords = new Set([
      'this', 'that', 'with', 'have', 'will', 'from', 'they', 'know',
      'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when',
      'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over',
      'such', 'take', 'than', 'them', 'well', 'were', 'what'
    ]);
    
    const filteredWords = words.filter(word => !stopWords.has(word));
    
    // Get unique words and sort by frequency
    const wordCount = new Map<string, number>();
    filteredWords.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });
    
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10) // Top 10 keywords
      .map(([word]) => word);
  }

  // Build knowledge base from multiple sources
  async buildKnowledgeBase(urls: string[]): Promise<void> {
    console.log('Building enhanced knowledge base...');

    const allChunks: KnowledgeChunk[] = [];

    // Scrape all URLs at once
    if (urls.length > 0) {
      console.log(`Scraping ${urls.length} URLs...`);
      const webChunks = await this.scrapeWebpages(urls);
      allChunks.push(...webChunks);
    }

    // Add static knowledge from existing JSON
    const staticChunks = await this.convertStaticKnowledge();
    allChunks.push(...staticChunks);

    this.knowledgeBase = {
      chunks: allChunks,
      lastUpdated: new Date().toISOString(),
      sources: urls
    };

    console.log(`Knowledge base built with ${allChunks.length} chunks`);
  }

  // Convert existing static knowledge to chunks
  private async convertStaticKnowledge(): Promise<KnowledgeChunk[]> {
    try {
      const response = await fetch('/company_knowledge.json');
      const data = await response.json();
      const chunks: KnowledgeChunk[] = [];
      
      // Convert FAQs
      if (data.faqs) {
        data.faqs.forEach((faq: any, index: number) => {
          chunks.push({
            id: `faq-${index}`,
            content: `Q: ${faq.question}\nA: ${faq.answer}`,
            source: 'static-faq',
            type: 'faq',
            keywords: faq.keywords || [],
            lastUpdated: new Date().toISOString()
          });
        });
      }
      
      // Convert services
      if (data.services) {
        data.services.forEach((service: any, index: number) => {
          chunks.push({
            id: `service-${index}`,
            content: `${service.name}: ${service.description}. ${service.detailed_description || ''}`,
            source: 'static-service',
            type: 'static',
            keywords: service.keywords || [],
            lastUpdated: new Date().toISOString()
          });
        });
      }
      
      return chunks;
    } catch (error) {
      console.error('Error converting static knowledge:', error);
      return [];
    }
  }

  // Search knowledge base with semantic similarity
  searchKnowledge(query: string, limit: number = 5): KnowledgeChunk[] {
    const queryWords = this.extractKeywords(query.toLowerCase());
    
    // Score each chunk based on keyword overlap and content relevance
    const scoredChunks = this.knowledgeBase.chunks.map(chunk => {
      let score = 0;
      
      // Keyword matching score
      const keywordOverlap = chunk.keywords.filter(keyword => 
        queryWords.some(queryWord => 
          keyword.includes(queryWord) || queryWord.includes(keyword)
        )
      ).length;
      score += keywordOverlap * 2;
      
      // Content matching score
      const contentLower = chunk.content.toLowerCase();
      queryWords.forEach(word => {
        if (contentLower.includes(word)) {
          score += 1;
        }
      });
      
      // Boost FAQ and service content
      if (chunk.type === 'faq') score += 1;
      if (chunk.type === 'static') score += 0.5;
      
      return { ...chunk, relevanceScore: score };
    });
    
    // Return top scoring chunks
    return scoredChunks
      .filter(chunk => chunk.relevanceScore! > 0)
      .sort((a, b) => b.relevanceScore! - a.relevanceScore!)
      .slice(0, limit);
  }

  // Get knowledge base stats
  getStats() {
    return {
      totalChunks: this.knowledgeBase.chunks.length,
      lastUpdated: this.knowledgeBase.lastUpdated,
      sources: this.knowledgeBase.sources,
      chunksByType: {
        webpage: this.knowledgeBase.chunks.filter(c => c.type === 'webpage').length,
        static: this.knowledgeBase.chunks.filter(c => c.type === 'static').length,
        faq: this.knowledgeBase.chunks.filter(c => c.type === 'faq').length
      }
    };
  }

  // Get the current knowledge base
  getKnowledgeBase(): EnhancedKnowledge {
    return this.knowledgeBase;
  }
}
